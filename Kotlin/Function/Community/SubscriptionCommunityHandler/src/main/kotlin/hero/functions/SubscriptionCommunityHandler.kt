package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.model.CommunityMemberStatus
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Instant

@Suppress("Unused")
class SubscriptionMessageThreadHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val logger: Logger = log,
) : PubSubSubscriber<SubscriberStatusChanged>(systemEnv) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: SubscriberStatusChanged) {
        val userId = payload.userId
        val creatorId = payload.creatorId

        val now = Instant.now()
        if (payload.statusChange == SubscriberStatusChange.UNSUBSCRIBED) {
            val communityIds = context
                .select(Tables.COMMUNITY.ID)
                .from(Tables.COMMUNITY_MEMBER)
                .join(Tables.COMMUNITY)
                .on(Tables.COMMUNITY.ID.eq(Tables.COMMUNITY_MEMBER.COMMUNITY_ID))
                .where(Tables.COMMUNITY_MEMBER.USER_ID.eq(userId))
                .and(Tables.COMMUNITY.OWNER_ID.eq(creatorId))
                .and(Tables.COMMUNITY_MEMBER.STATE.eq(CommunityMemberStatus.ACTIVE.name))
                .fetch()

            communityIds.forEach {
                val communityId = it.value1()
                logger.info("Removing $userId from community $communityId")
                context
                    .update(Tables.COMMUNITY_MEMBER)
                    .set(Tables.COMMUNITY_MEMBER.STATE, CommunityMemberStatus.SUBSCRIPTION_EXPIRED.name)
                    .set(Tables.COMMUNITY_MEMBER.LEFT_AT, now)
                    .where(Tables.COMMUNITY_MEMBER.COMMUNITY_ID.eq(communityId))
                    .and(Tables.COMMUNITY_MEMBER.USER_ID.eq(userId))
                    .execute()
            }
        } else {
            val creatorsCommunities = context
                .select(Tables.COMMUNITY.ID)
                .from(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.OWNER_ID.eq(creatorId))
                .and(Tables.COMMUNITY.DELETED_AT.isNull)
                .fetch()

            creatorsCommunities.forEach {
                val communityId = it.value1()
                logger.info("Adding $userId to community $communityId")

                context.insertInto(Tables.COMMUNITY_MEMBER)
                    .set(Tables.COMMUNITY_MEMBER.COMMUNITY_ID, communityId)
                    .set(Tables.COMMUNITY_MEMBER.USER_ID, userId)
                    .set(Tables.COMMUNITY_MEMBER.JOINED_AT, now)
                    .set(Tables.COMMUNITY_MEMBER.UPDATED_AT, now)
                    .set(Tables.COMMUNITY_MEMBER.STATE, CommunityMemberStatus.ACTIVE.name)
                    .execute()
            }
        }
    }
}
